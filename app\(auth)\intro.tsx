import { Image, Modal, View } from "react-native";
import { useFocusEffect, useRouter } from "expo-router";
import {
  Con<PERSON>er,
  LinkText,
  LinkButton,
  TopContainer,
  ButtonContainer,
  ModalContainer,
  ModalContent,
  ModalTitle,
  RoleButton,
  RoleButtonText,
  ModalButtonContainer,
} from "@/styles/Intro.styles";
import { useTranslation } from "react-i18next";
import { useAppDispatch } from "@/store/store";
import { resetUser, setUser } from "@/store/slices/authSlice";
import { UserType } from "@/types/api";
import { useCallback, useState } from "react";

export default function IntroScreen() {
  const router = useRouter();
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const [showModal, setShowModal] = useState(true);
  const [selectedRole, setSelectedRole] = useState<number | null>(null);

  useFocusEffect(
    useCallback(() => {
      dispatch(resetUser());
    }, [])
  );

  const handleRoleSelect = (role_id: number) => {
    setSelectedRole(role_id);
  };

  const handleContinue = () => {
    if (selectedRole) {
      dispatch(setUser({ role_id: selectedRole }));
      setShowModal(false);
      router.push("/(auth)/register");
    }
  };

  return (
    <Container contentContainerStyle={{ flex: 1 }}>
      <TopContainer>
        <Image
          source={require("../../assets/icon.png")}
          style={{ height: "80%", width: "100%" }}
          resizeMode="contain"
        />
      </TopContainer>

      <Modal
        visible={showModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowModal(false)}
      >
        <ModalContainer>
          <ModalContent>
            <ModalTitle>{t("intro.choose_role")}</ModalTitle>

            <ModalButtonContainer>
              <RoleButton
                isSelected={selectedRole === UserType.CUSTOMER}
                onPress={() => handleRoleSelect(UserType.CUSTOMER)}
              >
                <RoleButtonText isSelected={selectedRole === UserType.CUSTOMER}>
                  {t("intro.customer_button")}
                </RoleButtonText>
              </RoleButton>

              <RoleButton
                isSelected={selectedRole === UserType.VENDOR}
                onPress={() => handleRoleSelect(UserType.VENDOR)}
              >
                <RoleButtonText isSelected={selectedRole === UserType.VENDOR}>
                  {t("intro.vendor_button")}
                </RoleButtonText>
              </RoleButton>
            </ModalButtonContainer>

            {selectedRole && (
              <ButtonContainer
                variant="primary"
                onPress={handleContinue}
                title={t("intro.continue")}
                style={{ marginTop: 24, width: "100%" }}
              />
            )}

            <LinkButton onPress={() => router.push("/(auth)/login")}>
              <LinkText>{t("intro.have_account")}</LinkText>
            </LinkButton>
          </ModalContent>
        </ModalContainer>
      </Modal>
    </Container>
  );
}
